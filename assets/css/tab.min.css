/*!
 * # Semantic UI 2.5.0 - Tab
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */.vi-ui.tab{display:none}.vi-ui.tab.active,.vi-ui.tab.open{display:block}.vi-ui.tab.loading{position:relative;overflow:hidden;display:block;min-height:250px}.vi-ui.tab.loading *{position:relative!important;left:-10000px!important}.vi-ui.tab.loading.segment:before,.vi-ui.tab.loading:before{position:absolute;content:'';top:100px;left:50%;margin:-1.25em 0 0 -1.25em;width:2.5em;height:2.5em;border-radius:500rem;border:.2em solid rgba(0,0,0,.1)}.vi-ui.tab.loading.segment:after,.vi-ui.tab.loading:after{position:absolute;content:'';top:100px;left:50%;margin:-1.25em 0 0 -1.25em;width:2.5em;height:2.5em;-webkit-animation:button-spin .6s linear;animation:button-spin .6s linear;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;border-radius:500rem;border-color:#767676 transparent transparent;border-style:solid;border-width:.2em;-webkit-box-shadow:0 0 0 1px transparent;box-shadow:0 0 0 1px transparent}