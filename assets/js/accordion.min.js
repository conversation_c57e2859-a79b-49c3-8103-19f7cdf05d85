/*!
 * # Semantic UI 2.5.0 - Accordion
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
!function(e,n,i,t){"use strict";n=void 0!==n&&n.Math==Math?n:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),e.fn.vi_accordion=function(i){var t,o=e(this),a=(new Date).getTime(),s=[],l=arguments[0],r="string"==typeof l,c=[].slice.call(arguments,1);n.requestAnimationFrame||n.mozRequestAnimationFrame||n.webkitRequestAnimationFrame||n.msRequestAnimationFrame;return o.each(function(){var d,u,g=e.isPlainObject(i)?e.extend(!0,{},e.fn.vi_accordion.settings,i):e.extend({},e.fn.vi_accordion.settings),v=g.className,f=g.namespace,m=g.selector,p=g.error,h="."+f,b="module-"+f,y=o.selector||"",C=e(this),O=C.find(m.title),x=C.find(m.content),F=this,A=C.data(b);u={initialize:function(){u.debug("Initializing",C),u.bind.events(),g.observeChanges&&u.observeChanges(),u.instantiate()},instantiate:function(){A=u,C.data(b,u)},destroy:function(){u.debug("Destroying previous instance",C),C.off(h).removeData(b)},refresh:function(){O=C.find(m.title),x=C.find(m.content)},observeChanges:function(){"MutationObserver"in n&&((d=new MutationObserver(function(e){u.debug("DOM tree modified, updating selector cache"),u.refresh()})).observe(F,{childList:!0,subtree:!0}),u.debug("Setting up mutation observer",d))},bind:{events:function(){u.debug("Binding delegated events"),C.on(g.on+h,m.trigger,u.event.click)}},event:{click:function(){u.toggle.call(this)}},toggle:function(n){var i=void 0!==n?"number"==typeof n?O.eq(n):e(n).closest(m.title):e(this).closest(m.title),t=i.next(x),o=t.hasClass(v.animating),a=t.hasClass(v.active),s=a&&!o,l=!a&&o;u.debug("Toggling visibility of content",i),s||l?g.collapsible?u.close.call(i):u.debug("Cannot close vi_accordion content collapsing is disabled"):u.open.call(i)},open:function(n){var i=void 0!==n?"number"==typeof n?O.eq(n):e(n).closest(m.title):e(this).closest(m.title),t=i.next(x),o=t.hasClass(v.animating);t.hasClass(v.active)||o?u.debug("Accordion already open, skipping",t):(u.debug("Opening vi_accordion content",i),g.onOpening.call(t),g.onChanging.call(t),g.exclusive&&u.closeOthers.call(i),i.addClass(v.active),t.stop(!0,!0).addClass(v.animating),g.animateChildren&&(void 0!==e.fn.transition&&C.transition("is supported")?t.children().transition({animation:"fade in",queue:!1,useFailSafe:!0,debug:g.debug,verbose:g.verbose,duration:g.duration}):t.children().stop(!0,!0).animate({opacity:1},g.duration,u.resetOpacity)),t.slideDown(g.duration,g.easing,function(){t.removeClass(v.animating).addClass(v.active),u.reset.display.call(this),g.onOpen.call(this),g.onChange.call(this)}))},close:function(n){var i=void 0!==n?"number"==typeof n?O.eq(n):e(n).closest(m.title):e(this).closest(m.title),t=i.next(x),o=t.hasClass(v.animating),a=t.hasClass(v.active);!a&&!(!a&&o)||a&&o||(u.debug("Closing vi_accordion content",t),g.onClosing.call(t),g.onChanging.call(t),i.removeClass(v.active),t.stop(!0,!0).addClass(v.animating),g.animateChildren&&(void 0!==e.fn.transition&&C.transition("is supported")?t.children().transition({animation:"fade out",queue:!1,useFailSafe:!0,debug:g.debug,verbose:g.verbose,duration:g.duration}):t.children().stop(!0,!0).animate({opacity:0},g.duration,u.resetOpacity)),t.slideUp(g.duration,g.easing,function(){t.removeClass(v.animating).removeClass(v.active),u.reset.display.call(this),g.onClose.call(this),g.onChange.call(this)}))},closeOthers:function(n){var i,t,o,a=void 0!==n?O.eq(n):e(this).closest(m.title),s=a.parents(m.content).prev(m.title),l=a.closest(m.vi_accordion),r=m.title+"."+v.active+":visible",c=m.content+"."+v.active+":visible";g.closeNested?o=(i=l.find(r).not(s)).next(x):(i=l.find(r).not(s),t=l.find(c).find(r).not(s),o=(i=i.not(t)).next(x)),i.length>0&&(u.debug("Exclusive enabled, closing other content",i),i.removeClass(v.active),o.removeClass(v.animating).stop(!0,!0),g.animateChildren&&(void 0!==e.fn.transition&&C.transition("is supported")?o.children().transition({animation:"fade out",useFailSafe:!0,debug:g.debug,verbose:g.verbose,duration:g.duration}):o.children().stop(!0,!0).animate({opacity:0},g.duration,u.resetOpacity)),o.slideUp(g.duration,g.easing,function(){e(this).removeClass(v.active),u.reset.display.call(this)}))},reset:{display:function(){u.verbose("Removing inline display from element",this),e(this).css("display",""),""===e(this).attr("style")&&e(this).attr("style","").removeAttr("style")},opacity:function(){u.verbose("Removing inline opacity from element",this),e(this).css("opacity",""),""===e(this).attr("style")&&e(this).attr("style","").removeAttr("style")}},setting:function(n,i){if(u.debug("Changing setting",n,i),e.isPlainObject(n))e.extend(!0,g,n);else{if(void 0===i)return g[n];e.isPlainObject(g[n])?e.extend(!0,g[n],i):g[n]=i}},internal:function(n,i){if(u.debug("Changing internal",n,i),void 0===i)return u[n];e.isPlainObject(n)?e.extend(!0,u,n):u[n]=i},debug:function(){!g.silent&&g.debug&&(g.performance?u.performance.log(arguments):(u.debug=Function.prototype.bind.call(console.info,console,g.name+":"),u.debug.apply(console,arguments)))},verbose:function(){!g.silent&&g.verbose&&g.debug&&(g.performance?u.performance.log(arguments):(u.verbose=Function.prototype.bind.call(console.info,console,g.name+":"),u.verbose.apply(console,arguments)))},error:function(){g.silent||(u.error=Function.prototype.bind.call(console.error,console,g.name+":"),u.error.apply(console,arguments))},performance:{log:function(e){var n,i;g.performance&&(i=(n=(new Date).getTime())-(a||n),a=n,s.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:F,"Execution Time":i})),clearTimeout(u.performance.timer),u.performance.timer=setTimeout(u.performance.display,500)},display:function(){var n=g.name+":",i=0;a=!1,clearTimeout(u.performance.timer),e.each(s,function(e,n){i+=n["Execution Time"]}),n+=" "+i+"ms",y&&(n+=" '"+y+"'"),(void 0!==console.group||void 0!==console.table)&&s.length>0&&(console.groupCollapsed(n),console.table?console.table(s):e.each(s,function(e,n){console.log(n.Name+": "+n["Execution Time"]+"ms")}),console.groupEnd()),s=[]}},invoke:function(n,i,o){var a,s,l,r=A;return i=i||c,o=F||o,"string"==typeof n&&void 0!==r&&(n=n.split(/[\. ]/),a=n.length-1,e.each(n,function(i,t){var o=i!=a?t+n[i+1].charAt(0).toUpperCase()+n[i+1].slice(1):n;if(e.isPlainObject(r[o])&&i!=a)r=r[o];else{if(void 0!==r[o])return s=r[o],!1;if(!e.isPlainObject(r[t])||i==a)return void 0!==r[t]?(s=r[t],!1):(u.error(p.method,n),!1);r=r[t]}})),e.isFunction(s)?l=s.apply(o,i):void 0!==s&&(l=s),e.isArray(t)?t.push(l):void 0!==t?t=[t,l]:void 0!==l&&(t=l),s}},r?(void 0===A&&u.initialize(),u.invoke(l)):(void 0!==A&&A.invoke("destroy"),u.initialize())}),void 0!==t?t:this},e.fn.vi_accordion.settings={name:"Accordion",namespace:"vi_accordion",silent:!1,debug:!1,verbose:!1,performance:!0,on:"click",observeChanges:!0,exclusive:!0,collapsible:!0,closeNested:!1,animateChildren:!0,duration:350,easing:"easeOutQuad",onOpening:function(){},onClosing:function(){},onChanging:function(){},onOpen:function(){},onClose:function(){},onChange:function(){},error:{method:"The method you called is not defined"},className:{active:"active",animating:"animating"},selector:{vi_accordion:".accordion",title:".title",trigger:".title",content:".content"}},e.extend(e.easing,{easeOutQuad:function(e,n,i,t,o){return-t*(n/=o)*(n-2)+i}})}(jQuery,window,document);