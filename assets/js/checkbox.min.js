/*!
 * # Semantic UI 2.5.0 - Checkbox
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
!function(D,S,E,O){"use strict";S=void 0!==S&&S.Math==Math?S:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),D.fn.checkbox=function(k){var m,e=D(this),v=e.selector||"",y=(new Date).getTime(),C=[],x=k,w="string"==typeof x,I=[].slice.call(arguments,1);return e.each(function(){var e,r,i=D.extend(!0,{},D.fn.checkbox.settings,k),n=i.className,t=i.namespace,o=i.selector,l=i.error,a="."+t,c="module-"+t,d=D(this),s=D(this).children(o.label),u=D(this).children(o.input),b=u[0],h=!1,g=!1,f=d.data(c),p=this;r={initialize:function(){r.verbose("Initializing checkbox",i),r.create.label(),r.bind.events(),r.set.tabbable(),r.hide.input(),r.observeChanges(),r.instantiate(),r.setup()},instantiate:function(){r.verbose("Storing instance of module",r),f=r,d.data(c,r)},destroy:function(){r.verbose("Destroying module"),r.unbind.events(),r.show.input(),d.removeData(c)},fix:{reference:function(){d.is(o.input)&&(r.debug("Behavior called on <input> adjusting invoked element"),d=d.closest(o.checkbox),r.refresh())}},setup:function(){r.set.initialLoad(),r.is.indeterminate()?(r.debug("Initial value is indeterminate"),r.indeterminate()):r.is.checked()?(r.debug("Initial value is checked"),r.check()):(r.debug("Initial value is unchecked"),r.uncheck()),r.remove.initialLoad()},refresh:function(){s=d.children(o.label),u=d.children(o.input),b=u[0]},hide:{input:function(){r.verbose("Modifying <input> z-index to be unselectable"),u.addClass(n.hidden)}},show:{input:function(){r.verbose("Modifying <input> z-index to be selectable"),u.removeClass(n.hidden)}},observeChanges:function(){"MutationObserver"in S&&((e=new MutationObserver(function(e){r.debug("DOM tree modified, updating selector cache"),r.refresh()})).observe(p,{childList:!0,subtree:!0}),r.debug("Setting up mutation observer",e))},attachEvents:function(e,n){var t=D(e);n=D.isFunction(r[n])?r[n]:r.toggle,0<t.length?(r.debug("Attaching checkbox events to element",e,n),t.on("click"+a,n)):r.error(l.notFound)},event:{click:function(e){var n=D(e.target);n.is(o.input)?r.verbose("Using default check action on initialized checkbox"):n.is(o.link)?r.debug("Clicking link inside checkbox, skipping toggle"):(r.toggle(),u.focus(),e.preventDefault())},keydown:function(e){var n=e.which,t=13,i=32;g=n==27?(r.verbose("Escape key pressed blurring field"),u.blur(),!0):!(e.ctrlKey||n!=i&&n!=t)&&(r.verbose("Enter/space key pressed, toggling checkbox"),r.toggle(),!0)},keyup:function(e){g&&e.preventDefault()}},check:function(){r.should.allowCheck()&&(r.debug("Checking checkbox",u),r.set.checked(),r.should.ignoreCallbacks()||(i.onChecked.call(b),i.onChange.call(b)))},uncheck:function(){r.should.allowUncheck()&&(r.debug("Unchecking checkbox"),r.set.unchecked(),r.should.ignoreCallbacks()||(i.onUnchecked.call(b),i.onChange.call(b)))},indeterminate:function(){r.should.allowIndeterminate()?r.debug("Checkbox is already indeterminate"):(r.debug("Making checkbox indeterminate"),r.set.indeterminate(),r.should.ignoreCallbacks()||(i.onIndeterminate.call(b),i.onChange.call(b)))},determinate:function(){r.should.allowDeterminate()?r.debug("Checkbox is already determinate"):(r.debug("Making checkbox determinate"),r.set.determinate(),r.should.ignoreCallbacks()||(i.onDeterminate.call(b),i.onChange.call(b)))},enable:function(){r.is.enabled()?r.debug("Checkbox is already enabled"):(r.debug("Enabling checkbox"),r.set.enabled(),i.onEnable.call(b),i.onEnabled.call(b))},disable:function(){r.is.disabled()?r.debug("Checkbox is already disabled"):(r.debug("Disabling checkbox"),r.set.disabled(),i.onDisable.call(b),i.onDisabled.call(b))},get:{radios:function(){var e=r.get.name();return D('input[name="'+e+'"]').closest(o.checkbox)},otherRadios:function(){return r.get.radios().not(d)},name:function(){return u.attr("name")}},is:{initialLoad:function(){return h},radio:function(){return u.hasClass(n.radio)||"radio"==u.attr("type")},indeterminate:function(){return u.prop("indeterminate")!==O&&u.prop("indeterminate")},checked:function(){return u.prop("checked")!==O&&u.prop("checked")},disabled:function(){return u.prop("disabled")!==O&&u.prop("disabled")},enabled:function(){return!r.is.disabled()},determinate:function(){return!r.is.indeterminate()},unchecked:function(){return!r.is.checked()}},should:{allowCheck:function(){return r.is.determinate()&&r.is.checked()&&!r.should.forceCallbacks()?(r.debug("Should not allow check, checkbox is already checked"),!1):!1!==i.beforeChecked.apply(b)||(r.debug("Should not allow check, beforeChecked cancelled"),!1)},allowUncheck:function(){return r.is.determinate()&&r.is.unchecked()&&!r.should.forceCallbacks()?(r.debug("Should not allow uncheck, checkbox is already unchecked"),!1):!1!==i.beforeUnchecked.apply(b)||(r.debug("Should not allow uncheck, beforeUnchecked cancelled"),!1)},allowIndeterminate:function(){return r.is.indeterminate()&&!r.should.forceCallbacks()?(r.debug("Should not allow indeterminate, checkbox is already indeterminate"),!1):!1!==i.beforeIndeterminate.apply(b)||(r.debug("Should not allow indeterminate, beforeIndeterminate cancelled"),!1)},allowDeterminate:function(){return r.is.determinate()&&!r.should.forceCallbacks()?(r.debug("Should not allow determinate, checkbox is already determinate"),!1):!1!==i.beforeDeterminate.apply(b)||(r.debug("Should not allow determinate, beforeDeterminate cancelled"),!1)},forceCallbacks:function(){return r.is.initialLoad()&&i.fireOnInit},ignoreCallbacks:function(){return h&&!i.fireOnInit}},can:{change:function(){return!(d.hasClass(n.disabled)||d.hasClass(n.readOnly)||u.prop("disabled")||u.prop("readonly"))},uncheck:function(){return"boolean"==typeof i.uncheckable?i.uncheckable:!r.is.radio()}},set:{initialLoad:function(){h=!0},checked:function(){r.verbose("Setting class to checked"),d.removeClass(n.indeterminate).addClass(n.checked),r.is.radio()&&r.uncheckOthers(),r.is.indeterminate()||!r.is.checked()?(r.verbose("Setting state to checked",b),u.prop("indeterminate",!1).prop("checked",!0),r.trigger.change()):r.debug("Input is already checked, skipping input property change")},unchecked:function(){r.verbose("Removing checked class"),d.removeClass(n.indeterminate).removeClass(n.checked),r.is.indeterminate()||!r.is.unchecked()?(r.debug("Setting state to unchecked"),u.prop("indeterminate",!1).prop("checked",!1),r.trigger.change()):r.debug("Input is already unchecked")},indeterminate:function(){r.verbose("Setting class to indeterminate"),d.addClass(n.indeterminate),r.is.indeterminate()?r.debug("Input is already indeterminate, skipping input property change"):(r.debug("Setting state to indeterminate"),u.prop("indeterminate",!0),r.trigger.change())},determinate:function(){r.verbose("Removing indeterminate class"),d.removeClass(n.indeterminate),r.is.determinate()?r.debug("Input is already determinate, skipping input property change"):(r.debug("Setting state to determinate"),u.prop("indeterminate",!1))},disabled:function(){r.verbose("Setting class to disabled"),d.addClass(n.disabled),r.is.disabled()?r.debug("Input is already disabled, skipping input property change"):(r.debug("Setting state to disabled"),u.prop("disabled","disabled"),r.trigger.change())},enabled:function(){r.verbose("Removing disabled class"),d.removeClass(n.disabled),r.is.enabled()?r.debug("Input is already enabled, skipping input property change"):(r.debug("Setting state to enabled"),u.prop("disabled",!1),r.trigger.change())},tabbable:function(){r.verbose("Adding tabindex to checkbox"),u.attr("tabindex")===O&&u.attr("tabindex",0)}},remove:{initialLoad:function(){h=!1}},trigger:{change:function(){var e=E.createEvent("HTMLEvents"),n=u[0];n&&(r.verbose("Triggering native change event"),e.initEvent("change",!0,!1),n.dispatchEvent(e))}},create:{label:function(){0<u.prevAll(o.label).length?(u.prev(o.label).detach().insertAfter(u),r.debug("Moving existing label",s)):r.has.label()||(s=D("<label>").insertAfter(u),r.debug("Creating label",s))}},has:{label:function(){return 0<s.length}},bind:{events:function(){r.verbose("Attaching checkbox events"),d.on("click"+a,r.event.click).on("keydown"+a,o.input,r.event.keydown).on("keyup"+a,o.input,r.event.keyup)}},unbind:{events:function(){r.debug("Removing events"),d.off(a)}},uncheckOthers:function(){var e=r.get.otherRadios();r.debug("Unchecking other radios",e),e.removeClass(n.checked)},toggle:function(){r.can.change()?r.is.indeterminate()||r.is.unchecked()?(r.debug("Currently unchecked"),r.check()):r.is.checked()&&r.can.uncheck()&&(r.debug("Currently checked"),r.uncheck()):r.is.radio()||r.debug("Checkbox is read-only or disabled, ignoring toggle")},setting:function(e,n){if(r.debug("Changing setting",e,n),D.isPlainObject(e))D.extend(!0,i,e);else{if(n===O)return i[e];D.isPlainObject(i[e])?D.extend(!0,i[e],n):i[e]=n}},internal:function(e,n){if(D.isPlainObject(e))D.extend(!0,r,e);else{if(n===O)return r[e];r[e]=n}},debug:function(){!i.silent&&i.debug&&(i.performance?r.performance.log(arguments):(r.debug=Function.prototype.bind.call(console.info,console,i.name+":"),r.debug.apply(console,arguments)))},verbose:function(){!i.silent&&i.verbose&&i.debug&&(i.performance?r.performance.log(arguments):(r.verbose=Function.prototype.bind.call(console.info,console,i.name+":"),r.verbose.apply(console,arguments)))},error:function(){i.silent||(r.error=Function.prototype.bind.call(console.error,console,i.name+":"),r.error.apply(console,arguments))},performance:{log:function(e){var n,t;i.performance&&(t=(n=(new Date).getTime())-(y||n),y=n,C.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:p,"Execution Time":t})),clearTimeout(r.performance.timer),r.performance.timer=setTimeout(r.performance.display,500)},display:function(){var e=i.name+":",t=0;y=!1,clearTimeout(r.performance.timer),D.each(C,function(e,n){t+=n["Execution Time"]}),e+=" "+t+"ms",v&&(e+=" '"+v+"'"),(console.group!==O||console.table!==O)&&0<C.length&&(console.groupCollapsed(e),console.table?console.table(C):D.each(C,function(e,n){console.log(n.Name+": "+n["Execution Time"]+"ms")}),console.groupEnd()),C=[]}},invoke:function(i,e,n){var o,a,t,c=f;return e=e||I,n=p||n,"string"==typeof i&&c!==O&&(i=i.split(/[\. ]/),o=i.length-1,D.each(i,function(e,n){var t=e!=o?n+i[e+1].charAt(0).toUpperCase()+i[e+1].slice(1):i;if(D.isPlainObject(c[t])&&e!=o)c=c[t];else{if(c[t]!==O)return a=c[t],!1;if(!D.isPlainObject(c[n])||e==o)return c[n]!==O?a=c[n]:r.error(l.method,i),!1;c=c[n]}})),D.isFunction(a)?t=a.apply(n,e):a!==O&&(t=a),D.isArray(m)?m.push(t):m!==O?m=[m,t]:t!==O&&(m=t),a}},w?(f===O&&r.initialize(),r.invoke(x)):(f!==O&&f.invoke("destroy"),r.initialize())}),m!==O?m:this},D.fn.checkbox.settings={name:"Checkbox",namespace:"checkbox",silent:!1,debug:!1,verbose:!0,performance:!0,uncheckable:"auto",fireOnInit:!1,onChange:function(){},beforeChecked:function(){},beforeUnchecked:function(){},beforeDeterminate:function(){},beforeIndeterminate:function(){},onChecked:function(){},onUnchecked:function(){},onDeterminate:function(){},onIndeterminate:function(){},onEnable:function(){},onDisable:function(){},onEnabled:function(){},onDisabled:function(){},className:{checked:"checked",indeterminate:"indeterminate",disabled:"disabled",hidden:"hidden",radio:"radio",readOnly:"read-only"},error:{method:"The method you called is not defined"},selector:{checkbox:".vi-ui.checkbox",label:"label, .box",input:'input[type="checkbox"], input[type="radio"]',link:"a[href]"}}}(jQuery,window,document);