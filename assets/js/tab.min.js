/*!
 * # Semantic UI 2.5.0 - Tab
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
!function(e,t,a,n){"use strict";t=void 0!==t&&t.Math==Math?t:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),e.fn.viTab=function(n){var i,o=e.isFunction(this)?e(t):e(this),r=o.selector||"",s=(new Date).getTime(),c=[],l=arguments[0],d="string"==typeof l,u=[].slice.call(arguments,1),b=!1;return o.each(function(){var g,f,h,v,p,m,y=e.isPlainObject(n)?e.extend(!0,{},e.fn.viTab.settings,n):e.extend({},e.fn.viTab.settings),T=y.className,L=y.metadata,x=y.selector,A=y.error,P="."+y.namespace,C="module-"+y.namespace,F=e(this),S={},j=!0,E=0,O=this,w=F.data(C);p={initialize:function(){p.debug("Initializing tab menu item",F),p.fix.callbacks(),p.determineTabs(),p.debug("Determining tabs",y.context,f),y.auto&&p.set.auto(),p.bind.events(),y.history&&!b&&(p.initializeHistory(),b=!0),p.instantiate()},instantiate:function(){p.verbose("Storing instance of module",p),w=p,F.data(C,p)},destroy:function(){p.debug("Destroying tabs",F),F.removeData(C).off(P)},bind:{events:function(){e.isWindow(O)||(p.debug("Attaching tab activation events to element",F),F.on("click"+P,p.event.click))}},determineTabs:function(){var t;"parent"===y.context?(F.closest(x.vi-ui).length>0?(t=F.closest(x.vi-ui),p.verbose("Using closest UI element as parent",t)):t=F,g=t.parent(),p.verbose("Determined parent element for creating context",g)):y.context?(g=e(y.context),p.verbose("Using selector for tab context",y.context,g)):g=e("body"),y.childrenOnly?(f=g.children(x.tabs),p.debug("Searching tab context children for tabs",g,f)):(f=g.find(x.tabs),p.debug("Searching tab context for tabs",g,f))},fix:{callbacks:function(){e.isPlainObject(n)&&(n.onTabLoad||n.onTabInit)&&(n.onTabLoad&&(n.onLoad=n.onTabLoad,delete n.onTabLoad,p.error(A.legacyLoad,n.onLoad)),n.onTabInit&&(n.onFirstLoad=n.onTabInit,delete n.onTabInit,p.error(A.legacyInit,n.onFirstLoad)),y=e.extend(!0,{},e.fn.viTab.settings,n))}},initializeHistory:function(){if(p.debug("Initializing page state"),void 0===e.address)return p.error(A.state),!1;if("state"==y.historyType){if(p.debug("Using HTML5 to manage state"),!1===y.path)return p.error(A.path),!1;e.address.history(!0).state(y.path)}e.address.bind("change",p.event.history.change)},event:{click:function(t){var a=e(this).data(L.tab);void 0!==a?(y.history?(p.verbose("Updating page state",t),e.address.value(a)):(p.verbose("Changing tab",t),p.changeTab(a)),t.preventDefault()):p.debug("No tab specified")},history:{change:function(t){var a=t.pathNames.join("/")||p.get.initialPath(),n=y.templates.determineTitle(a)||!1;p.performance.display(),p.debug("History change event",a,t),m=t,void 0!==a&&p.changeTab(a),n&&e.address.title(n)}}},refresh:function(){h&&(p.debug("Refreshing tab",h),p.changeTab(h))},cache:{read:function(e){return void 0!==e&&S[e]},add:function(e,t){e=e||h,p.debug("Adding cached content for",e),S[e]=t},remove:function(e){e=e||h,p.debug("Removing cached content for",e),delete S[e]}},set:{auto:function(){var t="string"==typeof y.path?y.path.replace(/\/$/,"")+"/{$tab}":"/{$tab}";p.verbose("Setting up automatic tab retrieval from server",t),e.isPlainObject(y.apiSettings)?y.apiSettings.url=t:y.apiSettings={url:t}},loading:function(e){var t=p.get.tabElement(e);t.hasClass(T.loading)||(p.verbose("Setting loading state for",t),t.addClass(T.loading).siblings(f).removeClass(T.active+" "+T.loading),t.length>0&&y.onRequest.call(t[0],e))},state:function(t){e.address.value(t)}},changeTab:function(a){var n=t.history&&t.history.pushState&&y.ignoreFirstLoad&&j,i=y.auto||e.isPlainObject(y.apiSettings),o=i&&!n?p.utilities.pathToArray(a):p.get.defaultPathArray(a);a=p.utilities.arrayToPath(o),e.each(o,function(t,r){var s,c,l,d,u=o.slice(0,t+1),b=p.utilities.arrayToPath(u),f=p.is.tab(b),x=t+1==o.length,P=p.get.tabElement(b);if(p.verbose("Looking for tab",r),f){if(p.verbose("Tab was found",r),h=b,v=p.utilities.filterArray(o,u),x?d=!0:(c=o.slice(0,t+2),l=p.utilities.arrayToPath(c),(d=!p.is.tab(l))&&p.verbose("Tab parameters found",c)),d&&i)return n?(p.debug("Ignoring remote content on first tab load",b),j=!1,p.cache.add(a,P.html()),p.activate.all(b),y.onFirstLoad.call(P[0],b,v,m),y.onLoad.call(P[0],b,v,m)):(p.activate.navigation(b),p.fetch.content(b,a)),!1;p.debug("Opened local tab",b),p.activate.all(b),p.cache.read(b)||(p.cache.add(b,!0),p.debug("First time tab loaded calling tab init"),y.onFirstLoad.call(P[0],b,v,m)),y.onLoad.call(P[0],b,v,m)}else{if(-1!=a.search("/")||""===a)return p.error(A.missingTab,F,g,b),!1;if(b=(s=e("#"+a+', a[name="'+a+'"]')).closest("[data-tab]").data(L.tab),P=p.get.tabElement(b),s&&s.length>0&&b)return p.debug("Anchor link used, opening parent tab",P,s),P.hasClass(T.active)||setTimeout(function(){p.scrollTo(s)},0),p.activate.all(b),p.cache.read(b)||(p.cache.add(b,!0),p.debug("First time tab loaded calling tab init"),y.onFirstLoad.call(P[0],b,v,m)),y.onLoad.call(P[0],b,v,m),!1}})},scrollTo:function(t){var n=!!(t&&t.length>0)&&t.offset().top;!1!==n&&(p.debug("Forcing scroll to an in-page link in a hidden tab",n,t),e(a).scrollTop(n))},update:{content:function(t,a,n){var i=p.get.tabElement(t),o=i[0];n=void 0!==n?n:y.evaluateScripts,"string"==typeof y.cacheType&&"dom"==y.cacheType.toLowerCase()&&"string"!=typeof a?i.empty().append(e(a).clone(!0)):n?(p.debug("Updating HTML and evaluating inline scripts",t,a),i.html(a)):(p.debug("Updating HTML",t,a),o.innerHTML=a)}},fetch:{content:function(t,a){var n,i,o=p.get.tabElement(t),r={dataType:"html",encodeParameters:!1,on:"now",cache:y.alwaysRefresh,headers:{"X-Remote":!0},onSuccess:function(e){"response"==y.cacheType&&p.cache.add(a,e),p.update.content(t,e),t==h?(p.debug("Content loaded",t),p.activate.tab(t)):p.debug("Content loaded in background",t),y.onFirstLoad.call(o[0],t,v,m),y.onLoad.call(o[0],t,v,m),y.loadOnce?p.cache.add(a,!0):"string"==typeof y.cacheType&&"dom"==y.cacheType.toLowerCase()&&o.children().length>0?setTimeout(function(){var e=o.children().clone(!0);e=e.not("script"),p.cache.add(a,e)},0):p.cache.add(a,o.html())},urlData:{tab:a}},s=o.api("get request")||!1,c=s&&"pending"===s.state();a=a||t,i=p.cache.read(a),y.cache&&i?(p.activate.tab(t),p.debug("Adding cached content",a),y.loadOnce||("once"==y.evaluateScripts?p.update.content(t,i,!1):p.update.content(t,i)),y.onLoad.call(o[0],t,v,m)):c?(p.set.loading(t),p.debug("Content is already loading",a)):void 0!==e.api?(n=e.extend(!0,{},y.apiSettings,r),p.debug("Retrieving remote content",a,n),p.set.loading(t),o.api(n)):p.error(A.api)}},activate:{all:function(e){p.activate.tab(e),p.activate.navigation(e)},tab:function(e){var t=p.get.tabElement(e),a="siblings"==y.deactivate?t.siblings(f):f.not(t),n=t.hasClass(T.active);p.verbose("Showing tab content for",t),n||(t.addClass(T.active),a.removeClass(T.active+" "+T.loading),t.length>0&&y.onVisible.call(t[0],e))},navigation:function(e){var t=p.get.navElement(e),a="siblings"==y.deactivate?t.siblings(o):o.not(t),n=t.hasClass(T.active);p.verbose("Activating tab navigation for",t,e),n||(t.addClass(T.active),a.removeClass(T.active+" "+T.loading))}},deactivate:{all:function(){p.deactivate.navigation(),p.deactivate.tabs()},navigation:function(){o.removeClass(T.active)},tabs:function(){f.removeClass(T.active+" "+T.loading)}},is:{tab:function(e){return void 0!==e&&p.get.tabElement(e).length>0}},get:{initialPath:function(){return o.eq(0).data(L.tab)||f.eq(0).data(L.tab)},path:function(){return e.address.value()},defaultPathArray:function(e){return p.utilities.pathToArray(p.get.defaultPath(e))},defaultPath:function(e){var t=o.filter("[data-"+L.tab+'^="'+e+'/"]').eq(0).data(L.tab)||!1;if(t){if(p.debug("Found default tab",t),E<y.maxDepth)return E++,p.get.defaultPath(t);p.error(A.recursion)}else p.debug("No default tabs found for",e,f);return E=0,e},navElement:function(e){return e=e||h,o.filter("[data-"+L.tab+'="'+e+'"]')},tabElement:function(e){var t,a,n,i;return e=e||h,n=p.utilities.pathToArray(e),i=p.utilities.last(n),t=f.filter("[data-"+L.tab+'="'+e+'"]'),a=f.filter("[data-"+L.tab+'="'+i+'"]'),t.length>0?t:a},tab:function(){return h}},utilities:{filterArray:function(t,a){return e.grep(t,function(t){return-1==e.inArray(t,a)})},last:function(t){return!!e.isArray(t)&&t[t.length-1]},pathToArray:function(e){return void 0===e&&(e=h),"string"==typeof e?e.split("/"):[e]},arrayToPath:function(t){return!!e.isArray(t)&&t.join("/")}},setting:function(t,a){if(p.debug("Changing setting",t,a),e.isPlainObject(t))e.extend(!0,y,t);else{if(void 0===a)return y[t];e.isPlainObject(y[t])?e.extend(!0,y[t],a):y[t]=a}},internal:function(t,a){if(e.isPlainObject(t))e.extend(!0,p,t);else{if(void 0===a)return p[t];p[t]=a}},debug:function(){!y.silent&&y.debug&&(y.performance?p.performance.log(arguments):(p.debug=Function.prototype.bind.call(console.info,console,y.name+":"),p.debug.apply(console,arguments)))},verbose:function(){!y.silent&&y.verbose&&y.debug&&(y.performance?p.performance.log(arguments):(p.verbose=Function.prototype.bind.call(console.info,console,y.name+":"),p.verbose.apply(console,arguments)))},error:function(){y.silent||(p.error=Function.prototype.bind.call(console.error,console,y.name+":"),p.error.apply(console,arguments))},performance:{log:function(e){var t,a;y.performance&&(a=(t=(new Date).getTime())-(s||t),s=t,c.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:O,"Execution Time":a})),clearTimeout(p.performance.timer),p.performance.timer=setTimeout(p.performance.display,500)},display:function(){var t=y.name+":",a=0;s=!1,clearTimeout(p.performance.timer),e.each(c,function(e,t){a+=t["Execution Time"]}),t+=" "+a+"ms",r&&(t+=" '"+r+"'"),(void 0!==console.group||void 0!==console.table)&&c.length>0&&(console.groupCollapsed(t),console.table?console.table(c):e.each(c,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),c=[]}},invoke:function(t,a,n){var o,r,s,c=w;return a=a||u,n=O||n,"string"==typeof t&&void 0!==c&&(t=t.split(/[\. ]/),o=t.length-1,e.each(t,function(a,n){var i=a!=o?n+t[a+1].charAt(0).toUpperCase()+t[a+1].slice(1):t;if(e.isPlainObject(c[i])&&a!=o)c=c[i];else{if(void 0!==c[i])return r=c[i],!1;if(!e.isPlainObject(c[n])||a==o)return void 0!==c[n]?(r=c[n],!1):(p.error(A.method,t),!1);c=c[n]}})),e.isFunction(r)?s=r.apply(n,a):void 0!==r&&(s=r),e.isArray(i)?i.push(s):void 0!==i?i=[i,s]:void 0!==s&&(i=s),r}},d?(void 0===w&&p.initialize(),p.invoke(l)):(void 0!==w&&w.invoke("destroy"),p.initialize())}),void 0!==i?i:this},e.tab=function(){e(t).tab.apply(this,arguments)},e.fn.viTab.settings={name:"Tab",namespace:"tab",silent:!1,debug:!1,verbose:!1,performance:!0,auto:!1,history:!1,historyType:"hash",path:!1,context:!1,childrenOnly:!1,maxDepth:25,deactivate:"siblings",alwaysRefresh:!1,cache:!0,loadOnce:!1,cacheType:"response",ignoreFirstLoad:!1,apiSettings:!1,evaluateScripts:"once",onFirstLoad:function(e,t,a){},onLoad:function(e,t,a){},onVisible:function(e,t,a){},onRequest:function(e,t,a){},templates:{determineTitle:function(e){}},error:{api:"You attempted to load content without API module",method:"The method you called is not defined",missingTab:"Activated tab cannot be found. Tabs are case-sensitive.",noContent:"The tab you specified is missing a content url.",path:"History enabled, but no path was specified",recursion:"Max recursive depth reached",legacyInit:"onTabInit has been renamed to onFirstLoad in 2.0, please adjust your code.",legacyLoad:"onTabLoad has been renamed to onLoad in 2.0. Please adjust your code",state:"History requires Asual's Address library <https://github.com/asual/jquery-address>"},metadata:{tab:"tab",loaded:"loaded",promise:"promise"},className:{loading:"loading",active:"active"},selector:{tabs:".vi-ui.tab",ui:".vi-ui"}}}(jQuery,window,document);