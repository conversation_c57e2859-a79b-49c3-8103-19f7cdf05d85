/*!
 * # Semantic UI 2.5.0 - Form
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
!function(T,e,D,O){"use strict";e=void 0!==e&&e.Math==Math?e:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),T.fn.form=function(x){var k,E=T(this),w=E.selector||"",C=(new Date).getTime(),V=[],R=x,F=arguments[1],S="string"==typeof R,A=[].slice.call(arguments,1);return E.each(function(){var n,s,t,e,d,u,c,f,p,r,l,i,a,m,g,h,o=T(this),v=this,b=[],y=!1;(h={initialize:function(){h.get.settings(),S?(g===O&&h.instantiate(),h.invoke(R)):(g!==O&&g.invoke("destroy"),h.verbose("Initializing form validation",o,d),h.bindEvents(),h.set.defaults(),h.instantiate())},instantiate:function(){h.verbose("Storing instance of module",h),g=h,o.data(a,h)},destroy:function(){h.verbose("Destroying previous module",g),h.removeEvents(),o.removeData(a)},refresh:function(){h.verbose("Refreshing selector cache"),n=o.find(f.field),s=o.find(f.group),t=o.find(f.message),o.find(f.prompt),e=o.find(f.submit),o.find(f.clear),o.find(f.reset)},submit:function(){h.verbose("Submitting form",o),o.submit()},attachEvents:function(e,t){t=t||"submit",T(e).on("click"+m,function(e){h[t](),e.preventDefault()})},bindEvents:function(){h.verbose("Attaching form events"),o.on("submit"+m,h.validate.form).on("blur"+m,f.field,h.event.field.blur).on("click"+m,f.submit,h.submit).on("click"+m,f.reset,h.reset).on("click"+m,f.clear,h.clear),d.keyboardShortcuts&&o.on("keydown"+m,f.field,h.event.field.keydown),n.each(function(){var e=T(this),t=e.prop("type"),n=h.get.changeEvent(t,e);T(this).on(n+m,h.event.field.change)})},clear:function(){n.each(function(){var e=T(this),t=e.parent(),n=e.closest(s),r=n.find(f.prompt),i=e.data(c.defaultValue)||"",a=t.is(f.vi-uiCheckbox),o=t.is(f.vi-uiDropdown);n.hasClass(p.error)&&(h.verbose("Resetting error on field",n),n.removeClass(p.error),r.remove()),o?(h.verbose("Resetting dropdown value",t,i),t.dropdown("clear")):a?e.prop("checked",!1):(h.verbose("Resetting field value",e,i),e.val(""))})},reset:function(){n.each(function(){var e=T(this),t=e.parent(),n=e.closest(s),r=n.find(f.prompt),i=e.data(c.defaultValue),a=t.is(f.vi-uiCheckbox),o=t.is(f.vi-uiDropdown),l=n.hasClass(p.error);i!==O&&(l&&(h.verbose("Resetting error on field",n),n.removeClass(p.error),r.remove()),o?(h.verbose("Resetting dropdown value",t,i),t.dropdown("restore defaults")):a?(h.verbose("Resetting checkbox value",t,i),e.prop("checked",i)):(h.verbose("Resetting field value",e,i),e.val(i)))})},determine:{isValid:function(){var n=!0;return T.each(u,function(e,t){h.validate.field(t,e,!0)||(n=!1)}),n}},is:{bracketedRule:function(e){return e.type&&e.type.match(d.regExp.bracket)},shorthandFields:function(e){var t=e[Object.keys(e)[0]];return h.is.shorthandRules(t)},shorthandRules:function(e){return"string"==typeof e||T.isArray(e)},empty:function(e){return!e||0===e.length||(e.is('input[type="checkbox"]')?!e.is(":checked"):h.is.blank(e))},blank:function(e){return""===T.trim(e.val())},valid:function(e){var n=!0;return e?(h.verbose("Checking if field is valid",e),h.validate.field(u[e],e,!1)):(h.verbose("Checking if form is valid"),T.each(u,function(e,t){h.is.valid(e)||(n=!1)}),n)}},removeEvents:function(){o.off(m),n.off(m),e.off(m),n.off(m)},event:{field:{keydown:function(e){var t=T(this),n=e.which,r=t.is(f.input),i=t.is(f.checkbox),a=0<t.closest(f.vi-uiDropdown).length,o=13;n==27&&(h.verbose("Escape key pressed blurring field"),t.blur()),e.ctrlKey||n!=o||!r||a||i||(y||(t.one("keyup"+m,h.event.field.keyup),h.submit(),h.debug("Enter pressed on input submitting form")),y=!0)},keyup:function(){y=!1},blur:function(e){var t=T(this),n=t.closest(s),r=h.get.validation(t);n.hasClass(p.error)?(h.debug("Revalidating field",t,r),r&&h.validate.field(r)):"blur"==d.on&&r&&h.validate.field(r)},change:function(e){var t=T(this),n=t.closest(s),r=h.get.validation(t);r&&("change"==d.on||n.hasClass(p.error)&&d.revalidate)&&(clearTimeout(h.timer),h.timer=setTimeout(function(){h.debug("Revalidating field",t,h.get.validation(t)),h.validate.field(r)},d.delay))}}},get:{ancillaryValue:function(e){return!(!e.type||!e.value&&!h.is.bracketedRule(e))&&(e.value!==O?e.value:e.type.match(d.regExp.bracket)[1]+"")},ruleName:function(e){return h.is.bracketedRule(e)?e.type.replace(e.type.match(d.regExp.bracket)[0],""):e.type},changeEvent:function(e,t){return"checkbox"==e||"radio"==e||"hidden"==e||t.is("select")?"change":h.get.inputEvent()},inputEvent:function(){return D.createElement("input").oninput!==O?"input":D.createElement("input").onpropertychange!==O?"propertychange":"keyup"},fieldsFromShorthand:function(e){var r={};return T.each(e,function(n,e){"string"==typeof e&&(e=[e]),r[n]={rules:[]},T.each(e,function(e,t){r[n].rules.push({type:t})})}),r},prompt:function(e,t){var n,r,i=h.get.ruleName(e),a=h.get.ancillaryValue(e),o=h.get.field(t.identifier),l=o.val(),s=T.isFunction(e.prompt)?e.prompt(l):e.prompt||d.prompt[i]||d.text.unspecifiedRule,u=-1!==s.search("{value}"),c=-1!==s.search("{name}");return u&&(s=s.replace("{value}",o.val())),c&&(r=1==(n=o.closest(f.group).find("label").eq(0)).length?n.text():o.prop("placeholder")||d.text.unspecifiedField,s=s.replace("{name}",r)),s=(s=s.replace("{identifier}",t.identifier)).replace("{ruleValue}",a),e.prompt||h.verbose("Using default validation prompt for type",s,i),s},settings:function(){if(T.isPlainObject(x)){var e=Object.keys(x);0<e.length&&(x[e[0]].identifier!==O&&x[e[0]].rules!==O)?(d=T.extend(!0,{},T.fn.form.settings,F),u=T.extend({},T.fn.form.settings.defaults,x),h.error(d.error.oldSyntax,v),h.verbose("Extending settings from legacy parameters",u,d)):(x.fields&&h.is.shorthandFields(x.fields)&&(x.fields=h.get.fieldsFromShorthand(x.fields)),d=T.extend(!0,{},T.fn.form.settings,x),u=T.extend({},T.fn.form.settings.defaults,d.fields),h.verbose("Extending settings",u,d))}else d=T.fn.form.settings,u=T.fn.form.settings.defaults,h.verbose("Using default form validation",u,d);i=d.namespace,c=d.metadata,f=d.selector,p=d.className,r=d.regExp,l=d.error,a="module-"+i,m="."+i,g=o.data(a),h.refresh()},field:function(e){return h.verbose("Finding field with identifier",e),e=h.escape.string(e),0<n.filter("#"+e).length?n.filter("#"+e):0<n.filter('[name="'+e+'"]').length?n.filter('[name="'+e+'"]'):0<n.filter('[name="'+e+'[]"]').length?n.filter('[name="'+e+'[]"]'):0<n.filter("[data-"+c.validate+'="'+e+'"]').length?n.filter("[data-"+c.validate+'="'+e+'"]'):T("<input/>")},fields:function(e){var n=T();return T.each(e,function(e,t){n=n.add(h.get.field(t))}),n},validation:function(n){var r,i;return!!u&&(T.each(u,function(e,t){i=t.identifier||e,h.get.field(i)[0]==n[0]&&(t.identifier=i,r=t)}),r||!1)},value:function(e){var t=[];return t.push(e),h.get.values.call(v,t)[e]},values:function(e){var t=T.isArray(e)?h.get.fields(e):n,u={};return t.each(function(e,t){var n=T(t),r=(n.prop("type"),n.prop("name")),i=n.val(),a=n.is(f.checkbox),o=n.is(f.radio),l=-1!==r.indexOf("[]"),s=!!a&&n.is(":checked");r&&(l?(r=r.replace("[]",""),u[r]||(u[r]=[]),a?s?u[r].push(i||!0):u[r].push(!1):u[r].push(i)):o?u[r]!==O&&0!=u[r]||(u[r]=!!s&&(i||!0)):u[r]=a?!!s&&(i||!0):i)}),u}},has:{field:function(e){return h.verbose("Checking for existence of a field with identifier",e),"string"!=typeof(e=h.escape.string(e))&&h.error(l.identifier,e),0<n.filter("#"+e).length||(0<n.filter('[name="'+e+'"]').length||0<n.filter("[data-"+c.validate+'="'+e+'"]').length)}},escape:{string:function(e){return(e=String(e)).replace(r.escape,"\\$&")}},add:{rule:function(e,t){h.add.field(e,t)},field:function(n,e){var r={};h.is.shorthandRules(e)?(e=T.isArray(e)?e:[e],r[n]={rules:[]},T.each(e,function(e,t){r[n].rules.push({type:t})})):r[n]=e,u=T.extend({},u,r),h.debug("Adding rules",r,u)},fields:function(e){var t;t=e&&h.is.shorthandFields(e)?h.get.fieldsFromShorthand(e):e,u=T.extend({},u,t)},prompt:function(e,t){var n=h.get.field(e).closest(s),r=n.children(f.prompt),i=0!==r.length;t="string"==typeof t?[t]:t,h.verbose("Adding field error state",e),n.addClass(p.error),d.inline&&(i||(r=d.templates.prompt(t)).appendTo(n),r.html(t[0]),i?h.verbose("Inline errors are disabled, no inline error added",e):d.transition&&T.fn.transition!==O&&o.transition("is supported")?(h.verbose("Displaying error with css transition",d.transition),r.transition(d.transition+" in",d.duration)):(h.verbose("Displaying error with fallback javascript animation"),r.fadeIn(d.duration)))},errors:function(e){h.debug("Adding form error messages",e),h.set.error(),t.html(d.templates.error(e))}},remove:{rule:function(n,e){var r=T.isArray(e)?e:[e];if(e==O)return h.debug("Removed all rules"),void(u[n].rules=[]);u[n]!=O&&T.isArray(u[n].rules)&&T.each(u[n].rules,function(e,t){-1!==r.indexOf(t.type)&&(h.debug("Removed rule",t.type),u[n].rules.splice(e,1))})},field:function(e){var t=T.isArray(e)?e:[e];T.each(t,function(e,t){h.remove.rule(t)})},rules:function(e,n){T.isArray(e)?T.each(fields,function(e,t){h.remove.rule(t,n)}):h.remove.rule(e,n)},fields:function(e){h.remove.field(e)},prompt:function(e){var t=h.get.field(e).closest(s),n=t.children(f.prompt);t.removeClass(p.error),d.inline&&n.is(":visible")&&(h.verbose("Removing prompt for field",e),d.transition&&T.fn.transition!==O&&o.transition("is supported")?n.transition(d.transition+" out",d.duration,function(){n.remove()}):n.fadeOut(d.duration,function(){n.remove()}))}},set:{success:function(){o.removeClass(p.error).addClass(p.success)},defaults:function(){n.each(function(){var e=T(this),t=0<e.filter(f.checkbox).length?e.is(":checked"):e.val();e.data(c.defaultValue,t)})},error:function(){o.removeClass(p.success).addClass(p.error)},value:function(e,t){var n={};return n[e]=t,h.set.values.call(v,n)},values:function(e){T.isEmptyObject(e)||T.each(e,function(e,t){var n,r=h.get.field(e),i=r.parent(),a=T.isArray(t),o=i.is(f.vi-uiCheckbox),l=i.is(f.vi-uiDropdown),s=r.is(f.radio)&&o;0<r.length&&(a&&o?(h.verbose("Selecting multiple",t,r),i.checkbox("uncheck"),T.each(t,function(e,t){n=r.filter('[value="'+t+'"]'),i=n.parent(),0<n.length&&i.checkbox("check")})):s?(h.verbose("Selecting radio value",t,r),r.filter('[value="'+t+'"]').parent(f.vi-uiCheckbox).checkbox("check")):o?(h.verbose("Setting checkbox value",t,i),!0===t?i.checkbox("check"):i.checkbox("uncheck")):l?(h.verbose("Setting dropdown value",t,i),i.dropdown("set selected",t)):(h.verbose("Setting field value",t,r),r.val(t)))})}},validate:{form:function(e,t){var n=h.get.values();if(y)return!1;if(b=[],h.determine.isValid()){if(h.debug("Form has no validation errors, submitting"),h.set.success(),!0!==t)return d.onSuccess.call(v,e,n)}else if(h.debug("Form has errors"),h.set.error(),d.inline||h.add.errors(b),o.data("moduleApi")!==O&&e.stopImmediatePropagation(),!0!==t)return d.onFailure.call(v,b,n)},field:function(n,e,t){t=t===O||t,"string"==typeof n&&(h.verbose("Validating field",n),n=u[e=n]);var r=n.identifier||e,i=h.get.field(r),a=!!n.depends&&h.get.field(n.depends),o=!0,l=[];return n.identifier||(h.debug("Using field name as identifier",r),n.identifier=r),i.prop("disabled")?(h.debug("Field is disabled. Skipping",r),o=!0):n.optional&&h.is.blank(i)?(h.debug("Field is optional and blank. Skipping",r),o=!0):n.depends&&h.is.empty(a)?(h.debug("Field depends on another value that is not present or empty. Skipping",a),o=!0):n.rules!==O&&T.each(n.rules,function(e,t){h.has.field(r)&&!h.validate.rule(n,t)&&(h.debug("Field is invalid",r,t.type),l.push(h.get.prompt(t,n)),o=!1)}),o?(t&&(h.remove.prompt(r,l),d.onValid.call(i)),!0):(t&&(b=b.concat(l),h.add.prompt(r,l),d.onInvalid.call(i,l)),!1)},rule:function(e,t){var n=h.get.field(e.identifier),r=(t.type,n.val()),i=h.get.ancillaryValue(t),a=h.get.ruleName(t),o=d.rules[a];if(T.isFunction(o))return r=r===O||""===r||null===r?"":T.trim(r+""),o.call(n,r,i);h.error(l.noRule,a)}},setting:function(e,t){if(T.isPlainObject(e))T.extend(!0,d,e);else{if(t===O)return d[e];d[e]=t}},internal:function(e,t){if(T.isPlainObject(e))T.extend(!0,h,e);else{if(t===O)return h[e];h[e]=t}},debug:function(){!d.silent&&d.debug&&(d.performance?h.performance.log(arguments):(h.debug=Function.prototype.bind.call(console.info,console,d.name+":"),h.debug.apply(console,arguments)))},verbose:function(){!d.silent&&d.verbose&&d.debug&&(d.performance?h.performance.log(arguments):(h.verbose=Function.prototype.bind.call(console.info,console,d.name+":"),h.verbose.apply(console,arguments)))},error:function(){d.silent||(h.error=Function.prototype.bind.call(console.error,console,d.name+":"),h.error.apply(console,arguments))},performance:{log:function(e){var t,n;d.performance&&(n=(t=(new Date).getTime())-(C||t),C=t,V.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:v,"Execution Time":n})),clearTimeout(h.performance.timer),h.performance.timer=setTimeout(h.performance.display,500)},display:function(){var e=d.name+":",n=0;C=!1,clearTimeout(h.performance.timer),T.each(V,function(e,t){n+=t["Execution Time"]}),e+=" "+n+"ms",w&&(e+=" '"+w+"'"),1<E.length&&(e+=" ("+E.length+")"),(console.group!==O||console.table!==O)&&0<V.length&&(console.groupCollapsed(e),console.table?console.table(V):T.each(V,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),V=[]}},invoke:function(r,e,t){var i,a,n,o=g;return e=e||A,t=v||t,"string"==typeof r&&o!==O&&(r=r.split(/[\. ]/),i=r.length-1,T.each(r,function(e,t){var n=e!=i?t+r[e+1].charAt(0).toUpperCase()+r[e+1].slice(1):r;if(T.isPlainObject(o[n])&&e!=i)o=o[n];else{if(o[n]!==O)return a=o[n],!1;if(!T.isPlainObject(o[t])||e==i)return o[t]!==O&&(a=o[t]),!1;o=o[t]}})),T.isFunction(a)?n=a.apply(t,e):a!==O&&(n=a),T.isArray(k)?k.push(n):k!==O?k=[k,n]:n!==O&&(k=n),a}}).initialize()}),k!==O?k:this},T.fn.form.settings={name:"Form",namespace:"form",debug:!1,verbose:!1,performance:!0,fields:!1,keyboardShortcuts:!0,on:"submit",inline:!1,delay:200,revalidate:!0,transition:"scale",duration:200,onValid:function(){},onInvalid:function(){},onSuccess:function(){return!0},onFailure:function(){return!1},metadata:{defaultValue:"default",validate:"validate"},regExp:{htmlID:/^[a-zA-Z][\w:.-]*$/g,bracket:/\[(.*)\]/i,decimal:/^\d+\.?\d*$/,email:/^[a-z0-9!#$%&'*+\/=?^_`{|}~.-]+@[a-z0-9]([a-z0-9-]*[a-z0-9])?(\.[a-z0-9]([a-z0-9-]*[a-z0-9])?)*$/i,escape:/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,flags:/^\/(.*)\/(.*)?/,integer:/^\-?\d+$/,number:/^\-?\d*(\.\d+)?$/,url:/(https?:\/\/(?:www\.|(?!www))[^\s\.]+\.[^\s]{2,}|www\.[^\s]+\.[^\s]{2,})/i},text:{unspecifiedRule:"Please enter a valid value",unspecifiedField:"This field"},prompt:{empty:"{name} must have a value",checked:"{name} must be checked",email:"{name} must be a valid e-mail",url:"{name} must be a valid url",regExp:"{name} is not formatted correctly",integer:"{name} must be an integer",decimal:"{name} must be a decimal number",number:"{name} must be set to a number",is:'{name} must be "{ruleValue}"',isExactly:'{name} must be exactly "{ruleValue}"',not:'{name} cannot be set to "{ruleValue}"',notExactly:'{name} cannot be set to exactly "{ruleValue}"',contain:'{name} must contain "{ruleValue}"',containExactly:'{name} must contain exactly "{ruleValue}"',doesntContain:'{name} cannot contain  "{ruleValue}"',doesntContainExactly:'{name} cannot contain exactly "{ruleValue}"',minLength:"{name} must be at least {ruleValue} characters",length:"{name} must be at least {ruleValue} characters",exactLength:"{name} must be exactly {ruleValue} characters",maxLength:"{name} cannot be longer than {ruleValue} characters",match:"{name} must match {ruleValue} field",different:"{name} must have a different value than {ruleValue} field",creditCard:"{name} must be a valid credit card number",minCount:"{name} must have at least {ruleValue} choices",exactCount:"{name} must have exactly {ruleValue} choices",maxCount:"{name} must have {ruleValue} or less choices"},selector:{checkbox:'input[type="checkbox"], input[type="radio"]',clear:".clear",field:"input, textarea, select",group:".field",input:"input",message:".error.message",prompt:".prompt.label",radio:'input[type="radio"]',reset:'.reset:not([type="reset"])',submit:'.submit:not([type="submit"])',uiCheckbox:".vi-ui.checkbox",uiDropdown:".vi-ui.dropdown"},className:{error:"error",label:"ui prompt label",pressed:"down",success:"success"},error:{identifier:"You must specify a string identifier for each field",method:"The method you called is not defined.",noRule:"There is no rule matching the one you specified",oldSyntax:"Starting in 2.0 forms now only take a single settings object. Validation settings converted to new syntax automatically."},templates:{error:function(e){var n='<ul class="list">';return T.each(e,function(e,t){n+="<li>"+t+"</li>"}),T(n+="</ul>")},prompt:function(e){return T("<div/>").addClass("ui basic red pointing prompt label").html(e[0])}},rules:{empty:function(e){return!(e===O||""===e||T.isArray(e)&&0===e.length)},checked:function(){return 0<T(this).filter(":checked").length},email:function(e){return T.fn.form.settings.regExp.email.test(e)},url:function(e){return T.fn.form.settings.regExp.url.test(e)},regExp:function(e,t){if(t instanceof RegExp)return e.match(t);var n,r=t.match(T.fn.form.settings.regExp.flags);return r&&(t=2<=r.length?r[1]:t,n=3<=r.length?r[2]:""),e.match(new RegExp(t,n))},integer:function(e,t){var n,r,i,a=T.fn.form.settings.regExp.integer;return t&&-1===["",".."].indexOf(t)&&(-1==t.indexOf("..")?a.test(t)&&(n=r=t-0):(i=t.split("..",2),a.test(i[0])&&(n=i[0]-0),a.test(i[1])&&(r=i[1]-0))),a.test(e)&&(n===O||n<=e)&&(r===O||e<=r)},decimal:function(e){return T.fn.form.settings.regExp.decimal.test(e)},number:function(e){return T.fn.form.settings.regExp.number.test(e)},is:function(e,t){return t="string"==typeof t?t.toLowerCase():t,(e="string"==typeof e?e.toLowerCase():e)==t},isExactly:function(e,t){return e==t},not:function(e,t){return(e="string"==typeof e?e.toLowerCase():e)!=(t="string"==typeof t?t.toLowerCase():t)},notExactly:function(e,t){return e!=t},contains:function(e,t){return t=t.replace(T.fn.form.settings.regExp.escape,"\\$&"),-1!==e.search(new RegExp(t,"i"))},containsExactly:function(e,t){return t=t.replace(T.fn.form.settings.regExp.escape,"\\$&"),-1!==e.search(new RegExp(t))},doesntContain:function(e,t){return t=t.replace(T.fn.form.settings.regExp.escape,"\\$&"),-1===e.search(new RegExp(t,"i"))},doesntContainExactly:function(e,t){return t=t.replace(T.fn.form.settings.regExp.escape,"\\$&"),-1===e.search(new RegExp(t))},minLength:function(e,t){return e!==O&&e.length>=t},length:function(e,t){return e!==O&&e.length>=t},exactLength:function(e,t){return e!==O&&e.length==t},maxLength:function(e,t){return e!==O&&e.length<=t},match:function(e,t){var n;T(this);return 0<T('[data-validate="'+t+'"]').length?n=T('[data-validate="'+t+'"]').val():0<T("#"+t).length?n=T("#"+t).val():0<T('[name="'+t+'"]').length?n=T('[name="'+t+'"]').val():0<T('[name="'+t+'[]"]').length&&(n=T('[name="'+t+'[]"]')),n!==O&&e.toString()==n.toString()},different:function(e,t){var n;T(this);return 0<T('[data-validate="'+t+'"]').length?n=T('[data-validate="'+t+'"]').val():0<T("#"+t).length?n=T("#"+t).val():0<T('[name="'+t+'"]').length?n=T('[name="'+t+'"]').val():0<T('[name="'+t+'[]"]').length&&(n=T('[name="'+t+'[]"]')),n!==O&&e.toString()!==n.toString()},creditCard:function(n,e){var t,r,i={visa:{pattern:/^4/,length:[16]},amex:{pattern:/^3[47]/,length:[15]},mastercard:{pattern:/^5[1-5]/,length:[16]},discover:{pattern:/^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)/,length:[16]},unionPay:{pattern:/^(62|88)/,length:[16,17,18,19]},jcb:{pattern:/^35(2[89]|[3-8][0-9])/,length:[16]},maestro:{pattern:/^(5018|5020|5038|6304|6759|676[1-3])/,length:[12,13,14,15,16,17,18,19]},dinersClub:{pattern:/^(30[0-5]|^36)/,length:[14]},laser:{pattern:/^(6304|670[69]|6771)/,length:[16,17,18,19]},visaElectron:{pattern:/^(4026|417500|4508|4844|491(3|7))/,length:[16]}},a={},o=!1,l="string"==typeof e&&e.split(",");if("string"==typeof n&&0!==n.length){if(n=n.replace(/[\-]/g,""),l&&(T.each(l,function(e,t){(r=i[t])&&(a={length:-1!==T.inArray(n.length,r.length),pattern:-1!==n.search(r.pattern)}).length&&a.pattern&&(o=!0)}),!o))return!1;if((t={number:-1!==T.inArray(n.length,i.unionPay.length),pattern:-1!==n.search(i.unionPay.pattern)}).number&&t.pattern)return!0;for(var s=n.length,u=0,c=[[0,1,2,3,4,5,6,7,8,9],[0,2,4,6,8,1,3,5,7,9]],d=0;s--;)d+=c[u][parseInt(n.charAt(s),10)],u^=1;return d%10==0&&0<d}},minCount:function(e,t){return 0==t||(1==t?""!==e:e.split(",").length>=t)},exactCount:function(e,t){return 0==t?""===e:1==t?""!==e&&-1===e.search(","):e.split(",").length==t},maxCount:function(e,t){return 0!=t&&(1==t?-1===e.search(","):e.split(",").length<=t)}}}}(jQuery,window,document);