=== Faview - Virtual Reviews for WooCommerce ===
Contributors: villatheme, mrt3vn
Donate link: http://www.villatheme.com/donate
Tags: woocommerce reviews, virtual, reviews, virtual reviews, virtuals comment
Requires at least: 5.0
Tested up to: 6.8
WC tested up to: 9.8
Requires PHP: 7.0
Stable tag: trunk
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Faview - Virtual Reviews for WooCommerce generates and displays canned reviews to boost your customer engagement.

== Description ==

Faview enhances your WooCommerce review system with smart features that make leaving reviews easier and more engaging for customers. With predefined review content, canned reviews, and customization options, you can encourage more customer feedback while maintaining a seamless experience.

>[Try the Demo](http://new2new.com/?item=faview "Demo Faview Virtual Reviews for WooCommerce") | [Documents](http://docs.villatheme.com/?item=faview "Documents" ) |[Facebook group](https://www.facebook.com/groups/villatheme "VillaTheme")

[youtube https://www.youtube.com/watch?v=Xoxdgwv0JDM]

###FEATURES

&#9658; **Auto-Select 5 Stars**: Optionally prefill the rating with 5 stars while allowing customers to adjust as needed.

&#9658; **Auto-Fill Review Content**: Predefine a default review message (e.g., "Excellent product, highly recommended!") that appears in the review input field, which users can edit or remove.

&#9658; **Canned reviews**: Pre-written feedback that can be quickly inserted into a text field, reducing the need for users to type out a response manually.

- Build a list of predefined review content.

- Display your list of canned reviews as a dropdown or slider below the review form.

- Customize canned reviews appearance with text and background color, in default status or on hover

&#9658; **Purchased label**: Display a "Purchased" label when a review is submitted.

- Select an icon for the label.

- Customize its appearance to align with your store’s branding, including icon color, text color, and background color.

###Boost customer engagement and simplify the review process with Faview!

### 3rd party libraries & service
- This plugin relies on Semantic UI, Select2, jQuery Address to build the settings function to work properly.
&#9658; [Semantic UI: Form, Accordion, Button, Checkbox, Dropdown, Form, Grid, Icon, Input, Label, Menu, Message, Segment, Tab, Table, Transition ](http://github.com/semantic-org/semantic-ui/)
&#9658; [Select2](https://github.com/select2/select2/blob/master/LICENSE.md)
&#9658; [jQuery Address](https://jquery.com/license/)

### MAY BE YOU NEED

[VARGAL - Additional Variation Gallery for Woo](https://wordpress.org/plugins/vargal-additional-variation-gallery-for-woo)

[AFFI – Affiliate Marketing for WooCommerce](https://wordpress.org/plugins/affi-affiliate-marketing-for-woo)

[9Map – Map Multi Locations](https://wordpress.org/plugins/9map-map-multi-locations)

[TMDS - Dropshipping for TEMU and Woo](https://wordpress.org/plugins/tmds-dropshipping-for-temu-and-woo)

[DEPART - Deposit and Part payment for Woo](https://wordpress.org/plugins/depart-deposit-and-part-payment-for-woo)

[REES - Real Estate for Woo](https://wordpress.org/plugins/rees-real-estate-for-woo)

[HANDMADE - Dropshipping for Etsy and WooCommerce](https://wordpress.org/plugins/handmade-dropshipping-for-etsy-and-woo)

[HAPPY - Helpdesk Support Ticket System](https://wordpress.org/plugins/happy-helpdesk-support-ticket-system)

[GIFT4U - Gift Cards All in One for Woo](https://wordpress.org/plugins/gift4u-gift-cards-all-in-one-for-woo)

[SUBRE – Product Subscription for WooCommerce](https://wordpress.org/plugins/subre-product-subscription-for-woo)

[Clear Autoptimize Cache Automatically](https://wordpress.org/plugins/clear-autoptimize-cache-automatically)

[FEWC – WooCommerce Extra Checkout Fields](https://wordpress.org/plugins/fewc-extra-checkout-fields-for-woocommerce)

[EPOW – Custom Product Options for WooCommerce](https://wordpress.org/plugins/epow-custom-product-options-for-woocommerce)

[ChinaDS – Taobao Dropshipping for WooCommerce](https://wordpress.org/plugins/chinads-dropshipping-taobao-woocommerce)

[9MAIL – WordPress Email Templates Designer](https://wordpress.org/plugins/9mail-wp-email-templates-designer)

[EPOI – WP Points and Rewards](https://wordpress.org/plugins/epoi-wp-points-and-rewards)

[WebPOS – Point of Sale for WooCommerce](https://wordpress.org/plugins/webpos-point-of-sale-for-woocommerce)

[Jagif – WooCommerce Free Gift](https://wordpress.org/plugins/jagif-woo-free-gift)

[Coreem – Coupon Reminder for WooCommerce](https://wordpress.org/plugins/woo-coupon-reminder)

[COMPE – WooCommerce Compare Products](https://wordpress.org/plugins/compe-woo-compare-products)

[W2S – Migrate WooCommerce to Shopify](https://wordpress.org/plugins/w2s-migrate-woo-to-shopify)

[REDIS - WooCommerce Dynamic Pricing and Discounts](https://wordpress.org/plugins/redis-woo-dynamic-pricing-and-discounts)

[EXMAGE – WordPress Image Links](https://wordpress.org/plugins/exmage-wp-image-links)

[Pofily – WooCommerce Product Filters](https://wordpress.org/plugins/pofily-woo-product-filters)

[Bopo - WooCommerce Product Bundle Builder](https://wordpress.org/plugins/bopo-woo-product-bundle-builder)

[WPBulky – WordPress Bulk Edit Post Types](https://wordpress.org/plugins/wpbulky-wp-bulk-edit-post-types)

[Bulky - Bulk Edit Products for WooCommerce](https://wordpress.org/plugins/bulky-bulk-edit-products-for-woo)

[Catna – Woo Name Your Price and Offers](https://wordpress.org/plugins/catna-woo-name-your-price-and-offers)

[Product Size Chart For WooCommerce](https://wordpress.org/plugins/product-size-chart-for-woo)

[Product Pre-Orders for WooCommerce](https://wordpress.org/plugins/product-pre-orders-for-woo)

[Checkout Upsell Funnel for WooCommerce](https://wordpress.org/plugins/checkout-upsell-funnel-for-woo)

[Cart All In One For WooCommerce](https://wordpress.org/plugins/woo-cart-all-in-one)

[Email Template Customizer for WooCommerce](https://wordpress.org/plugins/email-template-customizer-for-woo)

[ALD - Dropshipping and Fulfillment for AliExpress and WooCommerce](https://wordpress.org/plugins/woo-alidropship)

[Product Variations Swatches for WooCommerce](https://wordpress.org/plugins/product-variations-swatches-for-woocommerce)

[Orders Tracking for WooCommerce](https://wordpress.org/plugins/woo-orders-tracking)

[Abandoned Cart Recovery For WooCommerce](https://wordpress.org/plugins/woo-abandoned-cart-recovery)

[Import Shopify to WooCommerce](https://wordpress.org/plugins/import-shopify-to-woocommerce)

[Customer Coupons for WooCommerce](https://wordpress.org/plugins/woo-customer-coupons)

[Virtual Reviews for WooCommerce](https://wordpress.org/plugins/woo-virtual-reviews)

[Thank You Page Customizer for WooCommerce](https://wordpress.org/plugins/woo-thank-you-page-customizer)

[Sales Countdown Timer](https://wordpress.org/plugins/sales-countdown-timer)

[Suggestion Engine for WooCommerce](https://wordpress.org/plugins/woo-suggestion-engine)

[EU Cookies Bar](https://wordpress.org/plugins/eu-cookies-bar)

[Lucky Wheel for WooCommerce](https://wordpress.org/plugins/woo-lucky-wheel)

[WordPress Lucky Wheel](https://wordpress.org/plugins/wp-lucky-wheel)

[Advanced Product Information for WooCommerce](https://wordpress.org/plugins/woo-advanced-product-information)

[LookBook for WooCommerce](https://wordpress.org/plugins/woo-lookbook)

[Photo Reviews for WooCommerce](https://wordpress.org/plugins/woo-photo-reviews)

[Product Builder for WooCommerce](https://wordpress.org/plugins/woo-product-builder)

[Boost Sales for WooCommerce](https://wordpress.org/plugins/woo-boost-sales)

[Free Shipping Bar for WooCommerce](https://wordpress.org/plugins/woo-free-shipping-bar)

[Notification for WooCommerce](https://wordpress.org/plugins/woo-notification)

[CURCY – Multi Currency for WooCommerce](https://wordpress.org/plugins/woo-multi-currency)

[Coupon Box for WooCommerce](https://wordpress.org/plugins/woo-coupon-box)

### Documentation

- [Getting Started](https://docs.villatheme.com/?item=faview)

### Plugin Links

- [Project Page](http://villatheme.com)
- [Documentation](https://docs.villatheme.com/?item=faview)
- [Report Bugs/Issues](https://villatheme.com/knowledge-base/security-is-our-priority/)

== Installation ==

1. Unzip the download package
1. Upload `woo-virtual-reviews` to the `/wp-content/plugins/` directory
1. Activate the plugin through the 'Plugins' menu in WordPress

== Frequently Asked Questions ==

== Screenshots ==
1. Add multiple review
2. After adding review

== Changelog ==
/**2.0.0 - 2025.03.19**/
- Updated: Remove manual add review feature
- Updated: Remove manual add reply feature

/**1.2.18 - 2025.01.03**/
- Updated: VillaTheme support

/**1.2.17 - 2024.11.23**/
– Updated: Compatible with WP 6.7 and WC 9.4
– Updated: Update support class

/**1.2.16 - 2024.06.17**/
– Updated: Compatible with WC 8.9

/**1.2.15 - 2024.04.13**/
– Updated: Compatible with WP 6.5 and  WC 8.7
– Updated: Update support class

/**1.2.14 – 2023.07.03*/
– Updated: Compatible with WooCommerce HPOS(COT)

/** 1.2.13 - *2023.02.21*/
– Updated: Add 'Virtual reviews' type to review filter at reviews page

/** 1.2.12 - *2022.11.19*/
- Fixed: Add review in all product page with quantity in range & PHP8

/** 1.2.11 - *2022.07.15*/
– Updated: Pro version URL

/** 1.2.10 - *2022.07.06*/
- Changed: Plugin name

/** 1.2.9 - *2022.07.05*/
- Changed: Plugin name
- Updated: Premium version URL
- Updated: Categories selected by term id

/** 1.2.7 - 2022.06.08**/
- Updated: Home plugin url
- Updated: check compatible with WordPress 6.0

/** 1.2.6 - 2022.05.26 **/
– Updated: Check Pro version active

/** 1.2.5 - 2022.03.29 **/
– Updated: VillaTheme_Support

/** 1.2.4 - 2022.03.24 **/
– Updated: Unlimit reviews

/** 1.2.3 - 2022.03.21 **/
– Updated: VillaTheme_Support

/**1.2.2 - 2022.02.22 **/
- Updated: Hook for show variation attributes label

/**1.2.1 - 2021.09.18 **/
- Updated: Hook for limit quantity

/**1.2.0 - 2021.09.15 **/
- Updated: Random quantity in range

/**1.1.2.4 - 2021.07.31 **/
- Updated: Update support file

/**1.1.2.3 - 2021.06.18 **/
- Updated: Update support file

/**1.1.2.2 - 2021.06.12 **/
- Fixed: Error with product was removed

/**1.1.2.1 - 2021.06.01 **/
- Update: Dashboard notices

**1.1.2- 2021.05.27**
- Fixed: Show purchased icon

**1.1.1- 2021.03.29**
- Fixed: Label row in setting page

**1.1.0- 2021.03.18**
- Updated: Fix 0 review bug

**1.0.5- 2020.09.18**
- Fixed: Comment count

**1.0.3- 2020.04.23**
- Fixed: Rate count, date

**1.0.2.6- 2020.04.23**
- Updated: Support file

/**1.0.2.4 - 2019.11.16**/
-Updated: Sanitize input data

/**1.0.2.3 - 2019.10.01**/
-Updated: Optimize generate reviews

/**1.0.2.2 - 2019.05.24**/
- Fixed: Import support file

/**1.0.2 - 2019.05.24**/
-Updated: Auto select rating & first comment
-Updated: Design purchased label feature
-Updated: Select style canned desktop, mobile independent
-Updated: Generate virtual reviews each product by ajax

/**1.0.1 - 2019.04.04**/
- Updated: Class support

/**1.0.0 - 2018.12.26**/
- Released

== Upgrade Notice ==