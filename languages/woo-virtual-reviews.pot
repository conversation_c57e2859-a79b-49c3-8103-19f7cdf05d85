#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Faview - Virtual Reviews for WooCommerce\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 02:19+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.7.1; wp-6.7.2\n"
"X-Domain: woo-virtual-reviews"

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:208
msgid "Add review with no comment"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:266
msgid ""
"Add your list comments display on front of your website (max = 50 sentences),"
" example:&#10I like it&#10Best product&#10Shipping fast"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:180
msgid ""
"Add your list virtual comments, example:&#10I like it&#10Best "
"product&#10Shipping fast"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:172
msgid "Add your list virtual names, example:&#10Alex&#10Anna&#10Ben"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:204
msgid "Assign comment to product group"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:169
msgid "Author"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:251
msgid "Auto fill review"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:244
msgid "Auto select 5 star"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:246
msgid "Auto select rating 5 star"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:294
msgid "Canned background color"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:296
msgid "Canned background color for slide style"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:308
msgid "Canned background hover color"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:310
msgid "Canned background hover color for slide style"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:263
msgid "Canned reviews"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:271
msgid "Canned style for Desktop"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:282
msgid "Canned style for front-end display (width device < 800px)"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:274
msgid "Canned style for front-end display (width device > 800px)"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:279
msgid "Canned style for Mobile"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:287
msgid "Canned text color"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:289
msgid "Canned text color for slide style"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:301
msgid "Canned text hover color"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:303
msgid "Canned text hover color for slide style"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:381
msgid "Custom CSS"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:214
msgid "Dropdown list"
msgstr ""

#. Name of the plugin
msgid "Faview - Virtual Reviews for WooCommerce"
msgstr ""

#. Description of the plugin
msgid ""
"Faview - Virtual Reviews for WooCommerce creates and display canned reviews "
"to increase your conversion rate."
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:141
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:142
msgid "Faview Reviews"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:152
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:153
msgid "Faview Settings"
msgstr ""

#. Author URI of the plugin
msgid "http://villatheme.com"
msgstr ""

#. URI of the plugin
msgid ""
"https://villatheme.com/extensions/faview-virtual-reviews-for-woocommerce/"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/display-comment.php:181
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/display-comment.php:189
msgid "product"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/display-comment.php:181
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/display-comment.php:189
msgid "products"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:326
msgid "Purchased icon"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:360
msgid "Purchased icon color"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:361
msgid "Purchased icon color for front-end display"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:327
msgid "Purchased icon for front-end display"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:427
msgid "Purchased label"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:374
msgid "Purchased label background color"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:375
msgid "Purchased label background color for front-end display"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:367
msgid "Purchased label text color"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:368
msgid "Purchased label text color for front-end display"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:192
msgid "Quantity of bought product"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:188
msgid "Random rating for generate multiple reviews"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/add-multi-reviews.php:59
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:185
msgid "Rating"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:229
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:398
msgid "Reply author"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:233
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:406
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:480
msgid "Reply content"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:419
msgid "Review form"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:177
msgid "Reviews"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/display-comment.php:102
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/display-comment.php:118
msgid "Sample comments"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:434
msgid "Save"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/woo-virtual-reviews.php:81
#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:413
msgid "Settings"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:257
msgid "Show canned reviews"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:321
msgid "Show purchased label"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:214
msgid "Slide"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:196
msgid "Unique author"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:200
msgid "Unique comment content"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:225
msgid "Use for real review"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:221
msgid "Use for virtual review"
msgstr ""

#. Author of the plugin
msgid "VillaTheme"
msgstr ""

#: E:/git-1224/free-version/woo-virtual-reviews/woo-virtual-reviews/includes/admin-settings.php:493
msgid "Your user role can't edit this option."
msgstr ""
